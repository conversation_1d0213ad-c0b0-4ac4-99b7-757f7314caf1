	Communiserver requirements

Users Roles:
	- Citizens: Local citizen in a village
	- Village leader(mudugudu): Local village leader
	- cell leader: Local cell leader
	- volunteer: A citizen who wants to volunteer for activites
	- Admin
	- AI Assistant

Permissions:
	- Citizens: 
				- can volunteer,
				- attend umuganda activity,
				- message fellow citizens,
				- crud their activity suggestion,
				- ask Questions,
				- can ask questions AI Assistant

	- Village leader (limited to his village): 
						- can attend umuganda activity,
						- can message all citizens,
						- can attend umuganda activity,
						- can create umuganda activity
						- can update umuganda activity,
						- can ask the activity to be cancelled (request forwded to cell leader),
						- can take attendance for activity,
						- can see village analytics,
						- can see villaveg activity suggestion.
						- can view asked questions,
						- can escalate question,

	- Cell leader (limitted to his cell villages): 
						- Add or change village leader,
						- approve activity cancelletion,
						- send message to all citizens,
	 					- can see cell activity analytics,
						- can see cell activity suggestions,
						- can view asked question,
					
	- Volunteer (extends citizen):
						- can take attendance,
						- can see volunteer work,
						- can accept or decline volunteer work

Entities:
	- Users:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Email string NOT NULL
		- PhoneNumber String NOT NULL
		- Password String
		- Role String
		- Profile Profile
	
	- Profiles:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Names String
		- ProfileImage String
		- Village Village
	
	- Cells:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- CellName String
		- CellLeader Profile
		- Villages List<Village>
	
	- Villages:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- VillageName String
		- VillageLeader Profile
		- Cell Cell
	
	- Messages:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- SenderId Profile
		- RecipientId Profile
		- message String
	
	- Notifications:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Message String
		- SenderId Profile
		- RecipientCells List<Cell>
	
	- Activity:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Description String
		- Date DateTime
		- Location String
		- Organizer Profile
	
	- Tasks:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Name String
		- Description String
		- AssignedTo List<Profile>
		- ActivityId Activity

	- Skill:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Name String
		- Description String
	
	- Suggestions:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- Name String
		- Description String
		- Location String
		- Suggester Profile
	
	- Attendances:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- ActivityId Activity
		- Participants List<Profile>
	
	- FeedBacks:
		- CreatedAt timestamp NOT NULL
		- UpdateAt timestamp NOT NULL
		- Deleted timestamp
		- Id uuid
		- UserId Profile
		- Rating Int
		- Comment String
		- ActivityId Activity