import { NextRequest, NextResponse } from "next/server";

// API endpoint URL
const API_URL = process.env.API_URL || "http://localhost:8000";

export async function GET(request: NextRequest) {
  try {
    // Get the access token from the request headers
    const authHeader = request.headers.get("Authorization");

    if (!authHeader) {
      return NextResponse.json(
        { message: "Authorization token is required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = searchParams.get("page") || "1";
    const size = searchParams.get("size") || "10";
    const q = searchParams.get("q") || "";
    const status = searchParams.get("status") || "";
    const villageId = searchParams.get("villageId") || "";
    const cellId = searchParams.get("cellId") || "";

    let url = `${API_URL}/api/v1/activities?page=${page}&size=${size}`;
    if (q) {
      url += `&q=${encodeURIComponent(q)}`;
    }
    if (status) {
      url += `&status=${encodeURIComponent(status)}`;
    }
    if (villageId) {
      url += `&villageId=${encodeURIComponent(villageId)}`;
    }
    if (cellId) {
      url += `&cellId=${encodeURIComponent(cellId)}`;
    }

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: authHeader,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in activities route handler:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the access token from the request headers
    const authHeader = request.headers.get("Authorization");

    if (!authHeader) {
      return NextResponse.json(
        { message: "Authorization token is required" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // No need to convert dates anymore as the backend now accepts string dates directly

    const response = await fetch(`${API_URL}/api/v1/activities`, {
      method: "POST",
      headers: {
        Authorization: authHeader,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in activities route handler:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
