"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, IconClipboardList, IconCircleCheck, IconAlertCircle, IconCalendar } from "@tabler/icons-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface TimeSeriesData {
  date: string;
  activities: number;
  tasks: number;
  reports: number;
  completedTasks: number;
}

export function RecentActivitiesTimeline() {
  const [timeSeriesData, setTimeSeriesData] = React.useState<TimeSeriesData[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchTimeSeriesData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const token = localStorage.getItem('accessToken');
        const response = await fetch('/api/v1/analytics/time-series?timeRange=7d', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch time series data: ${response.statusText}`);
        }

        const data = await response.json();
        // Sort by date descending to show most recent first
        const sortedData = data.sort((a: TimeSeriesData, b: TimeSeriesData) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        setTimeSeriesData(sortedData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTimeSeriesData();
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const getActivityLevel = (activities: number, tasks: number, reports: number) => {
    const total = activities + tasks + reports;
    if (total >= 10) return { level: "High", color: "bg-green-500", textColor: "text-green-600 dark:text-green-400" };
    if (total >= 5) return { level: "Medium", color: "bg-yellow-500", textColor: "text-yellow-600 dark:text-yellow-400" };
    if (total > 0) return { level: "Low", color: "bg-blue-500", textColor: "text-blue-600 dark:text-blue-400" };
    return { level: "None", color: "bg-gray-500", textColor: "text-gray-600 dark:text-gray-400" };
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconClock className="h-5 w-5" />
            Recent Activity Timeline
          </CardTitle>
          <CardDescription>Daily activity summary for the past week</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-full" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconClock className="h-5 w-5" />
            Recent Activity Timeline
          </CardTitle>
          <CardDescription>Error loading timeline data</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconClock className="h-5 w-5" />
          Recent Activity Timeline
        </CardTitle>
        <CardDescription>
          Daily activity summary for the past 7 days
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="max-h-[400px] overflow-y-auto">
          <div className="space-y-4">
            {timeSeriesData.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <IconCalendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No activity data available</p>
              </div>
            ) : (
              timeSeriesData.map((day, index) => {
                const activityLevel = getActivityLevel(day.activities, day.tasks, day.reports);
                const completionRate = day.tasks > 0 ? Math.round((day.completedTasks / day.tasks) * 100) : 0;

                return (
                  <div key={day.date} className="flex items-start space-x-4 relative">
                    {/* Timeline line */}
                    {index < timeSeriesData.length - 1 && (
                      <div className="absolute left-5 top-12 w-px h-16 bg-border" />
                    )}

                    {/* Activity indicator */}
                    <div className={`w-10 h-10 rounded-full ${activityLevel.color} flex items-center justify-center text-white relative z-10`}>
                      <IconClipboardList className="h-5 w-5" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium">{formatDate(day.date)}</h4>
                        <Badge variant="outline" className={activityLevel.textColor}>
                          {activityLevel.level} Activity
                        </Badge>
                      </div>

                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="flex items-center gap-1">
                          <IconClipboardList className="h-3 w-3 text-muted-foreground" />
                          <span className="text-muted-foreground">Activities:</span>
                          <span className="font-medium">{day.activities}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <IconCircleCheck className="h-3 w-3 text-muted-foreground" />
                          <span className="text-muted-foreground">Tasks:</span>
                          <span className="font-medium">{day.tasks}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <IconAlertCircle className="h-3 w-3 text-muted-foreground" />
                          <span className="text-muted-foreground">Reports:</span>
                          <span className="font-medium">{day.reports}</span>
                        </div>
                      </div>

                      {day.tasks > 0 && (
                        <div className="mt-2 flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">Task completion:</span>
                          <Badge variant={completionRate >= 70 ? "default" : completionRate >= 50 ? "secondary" : "destructive"} className="text-xs">
                            {completionRate}%
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            ({day.completedTasks}/{day.tasks})
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
