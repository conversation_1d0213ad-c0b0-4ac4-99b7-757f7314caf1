import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsS<PERSON> } from "class-validator";

export namespace CreateVillageLeaderDTO {
  export class Input {
    @IsString()
    @IsNotEmpty()
    names: string;

    @IsEmail()
    @IsNotEmpty()
    email: string;

    @IsString()
    @IsNotEmpty()
    phone: string;

    @IsString()
    @IsNotEmpty()
    cellId: string;

    @IsString()
    @IsNotEmpty()
    villageId: string;
  }
}
