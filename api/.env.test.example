# Application Configuration
PORT=8000
NODE_ENV=dev
ALLOWED_ORIGINS=http://localhost:8000

#SEEDING Configuration
BACKDOOR_ENABLED=false
BACKDOOR_USERNAME=<EMAIL>
BACKDOOR_PASSWORD=password

# Database Configuration
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=testDB
DB_HOST=localhost
DB_PORT=5433

# PAYMENT GATEWAY USER
GATEWAY_USERNAME="testGtwy"
GATEWAY_PASSWORD="password"

# PAYMENT GATEWAY DETAILS
MERCHANT_CODE="merchant"
SERVICE_CODE="service"
URUBUTO_BASE_URL="init payment url"
REDIRECTION_URL="communiserver redirection url"
AUTHENTICATION_TOKEN="api key"

# JWT Configuration
JWT_SECRET=mySecretKeyIdontWantToKeep
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=myRefreshKeyIdontWantToKeep
JWT_REFRESH_EXPIRES_IN=1d
JWT_GATEWAY_SECRET=myGatewayKeyIdontWantToKeep
JWT_GATEWAY_EXPIRES_IN=1d

# SWAGGER Configuration
swaggerEnabled=true

#ADMIN Configuration
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=password
PGADMIN_PORT=80

# EMAIL Configuration
SENDGRID_API_KEY=sendfric_api_key
EMAIL_FROM=<EMAIL>
CLIENT_URL=https://communiserver.awesomity.rw