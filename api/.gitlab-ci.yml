image: docker:git
services:
  - docker:dind

stages:
  - check-dev
  - docker_image_build
  - deploy

variables:
  BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  NODE_IMAGE_VERSION: node:20-alpine
  BASE_DEV_SERVER_PATH: /home/<USER>/communiserver/api
  DEV_PROJECT_NAME: communiserver-dev

.build_dockerfile_template: &build_dockerfile_template
  stage: docker_image_build
  script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker build -f Dockerfile -t $BASE_TAG_NAME:$CI_COMMIT_SHORT_SHA -t $BASE_TAG_NAME:latest .
    - docker push $BASE_TAG_NAME:$CI_COMMIT_SHORT_SHA
    - docker push $BASE_TAG_NAME:latest

.deploy_template: &deploy_template
  script:
    - "which ssh-agent || (  apk update  && apk add openssh-client )"
    - "which rsync || ( apk update  && apk add rsync  )"
    - eval $(ssh-agent -s)
    - echo "$PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $PUBLIC_IP_ADDRESS >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - echo "Deploying to $DEPLOY_ENVIRONMENT"
    - which envsubst || apk add gettext
    - envsubst '${BASE_TAG_NAME}' < /$CI_PROJECT_DIR/compose/$COMPOSE_FILE_NAME > compose.yml
    - echo $CI_PROJECT_DIR
    - rsync --rsync-path=/usr/bin/rsync --delete -avuz --exclude=".*" compose.yml $USER@$PUBLIC_IP_ADDRESS:$BASE_SERVER_PATH
    - rsync --rsync-path=/usr/bin/rsync --delete -avuz --exclude=".*" $ENV_FILE $USER@$PUBLIC_IP_ADDRESS:$BASE_SERVER_PATH/.env
    - echo "STARTING DOCKER IMAGE"
    - ssh $USER@$PUBLIC_IP_ADDRESS "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY &&
      cd $BASE_SERVER_PATH &&
      docker image rm -f $BASE_TAG_NAME:latest &&
      docker pull $BASE_TAG_NAME:latest &&
      docker compose -f compose.yml -p $PROJECT_NAME down -v --rmi all &&
      docker compose -f compose.yml -p $PROJECT_NAME up -d"

eslint:
  stage: check-dev
  before_script:
    - npm install
    - npm run build
  script:
    - npm run lint
  image: $NODE_IMAGE_VERSION
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'dev'

run_test:
  stage: check-dev
  coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
  services:
    - postgres:16.4-alpine3.20
  variables:
    POSTGRES_DB: testDB
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: password
    POSTGRES_HOST_AUTH_METHOD: trust
  artifacts:
    when: always
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
  image: $NODE_IMAGE_VERSION
  before_script:
    - npm install
    - echo "*********** Creating .env file... ************"
    - cat $TEST_ENV >> .env
    - npm run typeorm:run-migration
  after_script:
    - echo "*********** Deleting .env file after tests... ************"
    - rm .env
  script:
    - npm run test:e2e
    - echo "*********** Run tests here. To be fixed later... ************"
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'dev'

build_docker_file:
  stage: docker_image_build
  <<: *build_dockerfile_template
  only:
    - dev
    - staging
    - main

deploy_to_dev_environment:
  stage: deploy
  <<: *deploy_template
  variables:
    PRIVATE_KEY: $DEV_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $DEV_PUBLIC_IP_ADDRESS
    USER: $DEV_USER
    BASE_SERVER_PATH: $BASE_DEV_SERVER_PATH
    COMPOSE_FILE_NAME: dev.compose.yml
    ENV_FILE: $DEV_ENV
    PROJECT_NAME: $DEV_PROJECT_NAME
  only:
    - dev
